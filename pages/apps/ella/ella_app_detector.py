"""
Ella应用检测器
负责检测各种应用的打开状态和权限状态
"""
import subprocess
from core.logger import log


class EllaAppDetector:
    """Ella应用检测器"""

    def __init__(self):
        """初始化应用检测器"""
        pass

    def check_weather_app_opened(self) -> bool:
        """
        检查是否有天气应用被打开
        
        Returns:
            bool: 是否有天气应用打开
        """
        try:
            log.info("检查天气应用状态")

            # 检查当前运行的应用
            result = subprocess.run(
                ["adb", "shell", "dumpsys", "activity", "activities"],
                capture_output=True,
                text=True,
                timeout=10
            )

            if result.returncode == 0:
                activity_output = result.stdout

                # 常见的天气应用包名
                weather_packages = [
                    "com.miui.weather",
                    "com.android.weather",
                    "com.google.android.apps.weather",
                    "com.transsion.weather",
                    "com.weather.forecast",
                    "com.accuweather",
                    "com.weather.channel",
                    "weather"
                ]

                for package in weather_packages:
                    if package in activity_output:
                        log.info(f"✅ 检测到天气应用: {package}")
                        return True

                log.info("未检测到专门的天气应用")
                return False
            else:
                log.error(f"获取应用活动失败: {result.stderr}")
                return False

        except Exception as e:
            log.error(f"检查天气应用失败: {e}")
            return False

    def check_camera_app_opened(self) -> bool:
        """
        检查是否有相机应用被打开
        
        Returns:
            bool: 是否有相机应用打开
        """
        try:
            log.info("检查相机应用状态")

            # 检查当前运行的应用
            result = subprocess.run(
                ["adb", "shell", "dumpsys", "activity", "activities"],
                capture_output=True,
                text=True,
                timeout=10
            )

            if result.returncode == 0:
                activity_output = result.stdout

                # 常见的相机应用包名
                camera_packages = [
                    "com.android.camera",
                    "com.google.android.GoogleCamera",
                    "com.transsion.camera",
                    "com.sec.android.app.camera",
                    "com.huawei.camera",
                    "com.xiaomi.camera",
                    "com.oppo.camera",
                    "com.vivo.camera",
                    "camera",
                    "Camera"
                ]

                for package in camera_packages:
                    if package in activity_output:
                        log.info(f"✅ 检测到相机应用: {package}")
                        return True

                log.info("未检测到专门的相机应用")
                return False
            else:
                log.error(f"获取应用活动失败: {result.stderr}")
                return False

        except Exception as e:
            log.error(f"检查相机应用失败: {e}")
            return False

    def check_contacts_app_opened(self) -> bool:
        """
        检查是否有联系人应用被打开

        Returns:
            bool: 是否有联系人应用打开
        """
        try:
            log.info("检查联系人应用状态")

            # 检查当前运行的应用
            result = subprocess.run(
                ["adb", "shell", "dumpsys", "activity", "activities"],
                capture_output=True,
                text=True,
                timeout=10
            )

            if result.returncode == 0:
                activity_output = result.stdout

                # 调试：输出部分activity信息
                log.info("=== 当前活动应用信息（前500字符）===")
                log.info(activity_output[:500])

                # 常见的联系人应用包名
                contacts_packages = [
                    "com.sh.smart.caller",  # Dalier联系人应用
                ]

                # 调试：检查每个包名
                for package in contacts_packages:
                    if package in activity_output:
                        log.info(f"✅ 检测到联系人应用: {package}")
                        return True
                    else:
                        log.debug(f"未找到包名: {package}")

                # 额外检查：使用更宽松的匹配
                log.info("=== 尝试宽松匹配 ===")
                if "smart.caller" in activity_output:
                    log.info("✅ 通过宽松匹配检测到 smart.caller")
                    return True

                if "caller" in activity_output.lower():
                    log.info("✅ 通过宽松匹配检测到 caller")
                    return True

                log.info("未检测到专门的联系人应用")
                return False
            else:
                log.error(f"获取应用活动失败: {result.stderr}")
                return False

        except Exception as e:
            log.error(f"检查联系人应用失败: {e}")
            return False

    def check_contacts_app_opened_alternative(self) -> bool:
        """
        使用替代方法检查联系人应用是否打开

        Returns:
            bool: 是否有联系人应用打开
        """
        try:
            log.info("使用替代方法检查联系人应用状态")

            # 方法1: 检查当前前台应用
            result1 = subprocess.run(
                ["adb", "shell", "dumpsys", "window", "windows"],
                capture_output=True,
                text=True,
                timeout=10
            )

            if result1.returncode == 0:
                window_output = result1.stdout
                log.info("=== 窗口信息（前300字符）===")
                log.info(window_output[:300])

                if "com.sh.smart.caller" in window_output:
                    log.info("✅ 通过窗口信息检测到Dalier应用")
                    return True

            # 方法2: 检查运行中的进程
            result2 = subprocess.run(
                ["adb", "shell", "ps"],
                capture_output=True,
                text=True,
                timeout=10
            )

            if result2.returncode == 0:
                ps_output = result2.stdout
                if "com.sh.smart.caller" in ps_output:
                    log.info("✅ 通过进程检查检测到Dalier应用")
                    return True
                elif "smart.caller" in ps_output:
                    log.info("✅ 通过进程检查检测到smart.caller")
                    return True

            log.info("所有替代方法都未检测到Dalier应用")
            return False

        except Exception as e:
            log.error(f"替代方法检查联系人应用失败: {e}")
            return False

    def check_camera_permission(self) -> bool:
        """
        检查相机权限状态
        
        Returns:
            bool: 是否有相机权限
        """
        try:
            log.info("检查相机权限状态")

            # 检查Ella应用的相机权限
            result = subprocess.run(
                ["adb", "shell", "dumpsys", "package", "com.transsion.aivoiceassistant"],
                capture_output=True,
                text=True,
                timeout=10
            )

            if result.returncode == 0:
                package_output = result.stdout

                # 查找相机权限相关信息
                camera_permission_indicators = [
                    "android.permission.CAMERA",
                    "CAMERA: granted=true",
                    "camera permission"
                ]

                for indicator in camera_permission_indicators:
                    if indicator in package_output:
                        log.info(f"✅ 检测到相机权限信息: {indicator}")
                        return True

                log.info("未检测到相机权限信息")
                return False
            else:
                log.error(f"获取权限信息失败: {result.stderr}")
                return False

        except Exception as e:
            log.error(f"检查相机权限失败: {e}")
            return False

    def check_clock_app_opened(self, clock_package_name: str = "com.transsion.deskclock") -> bool:
        """
        检查时钟应用是否打开
        
        Args:
            clock_package_name: 时钟应用包名
            
        Returns:
            bool: 时钟应用是否正在运行
        """
        try:
            log.info("检查时钟应用运行状态")

            # 方法1: 检查当前运行的应用
            result = subprocess.run(
                ["adb", "shell", "dumpsys", "activity", "activities"],
                capture_output=True,
                text=True,
                timeout=10
            )

            if result.returncode == 0:
                output = result.stdout
                if clock_package_name in output:
                    log.info(f"✅ 时钟应用正在运行: {clock_package_name}")
                    return True

            # 方法2: 检查当前焦点窗口
            result = subprocess.run(
                ["adb", "shell", "dumpsys", "window", "windows"],
                capture_output=True,
                text=True,
                timeout=10
            )

            if result.returncode == 0:
                output = result.stdout
                if clock_package_name in output and "mCurrentFocus" in output:
                    log.info(f"✅ 时钟应用在前台: {clock_package_name}")
                    return True

            # 方法3: 检查进程
            result = subprocess.run(
                ["adb", "shell", "ps", "|", "grep", clock_package_name],
                capture_output=True,
                text=True,
                timeout=5,
                shell=True
            )

            if result.returncode == 0 and result.stdout.strip():
                log.info(f"✅ 时钟应用进程存在: {clock_package_name}")
                return True

            log.info(f"❌ 时钟应用未运行: {clock_package_name}")
            return False

        except Exception as e:
            log.error(f"检查时钟应用状态失败: {e}")
            return False

    def find_available_apps(self, app_type: str) -> list:
        """
        查找可用的特定类型应用
        
        Args:
            app_type: 应用类型 ('weather', 'camera', 'contacts', 'clock')
            
        Returns:
            list: 可用应用包名列表
        """
        try:
            log.info(f"查找可用的{app_type}应用")

            # 获取所有已安装的应用包
            result = subprocess.run(
                ["adb", "shell", "pm", "list", "packages"],
                capture_output=True,
                text=True,
                timeout=30
            )

            if result.returncode != 0:
                log.error(f"获取应用包列表失败: {result.stderr}")
                return []

            packages = result.stdout.strip().split('\n')
            found_apps = []

            # 定义不同类型应用的关键词
            app_keywords = {
                'weather': ['weather', 'clima', 'meteo'],
                'camera': ['camera', 'cam', 'photo'],
                'contacts': ['contacts', 'contact', 'people', 'dialer'],
                'clock': ['clock', 'time', 'alarm', 'timer', 'deskclock']
            }

            keywords = app_keywords.get(app_type, [])

            for package_line in packages:
                if package_line.startswith('package:'):
                    package_name = package_line.replace('package:', '')

                    # 检查是否包含相关关键词
                    for keyword in keywords:
                        if keyword.lower() in package_name.lower():
                            found_apps.append(package_name)
                            break

            log.info(f"找到{len(found_apps)}个{app_type}应用: {found_apps}")
            return found_apps

        except Exception as e:
            log.error(f"查找{app_type}应用失败: {e}")
            return []


if __name__ == '__main__':
    detector = EllaAppDetector()
    print(detector.check_contacts_app_opened())
