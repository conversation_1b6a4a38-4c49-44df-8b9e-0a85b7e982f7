{"uid": "4b4757e66a1912dae1a509f688f20b0f", "name": "categories", "children": [{"name": "Product defects", "children": [{"name": "AssertionError: 未收到AI响应\nassert False", "children": [{"name": "测试open bluetooth命令", "uid": "80d93ad84c102297", "parentUid": "aefce842ed38e08b6e76416e3ba9d1a8", "status": "failed", "time": {"start": 1750679165655, "stop": 1750679186246, "duration": 20591}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "aefce842ed38e08b6e76416e3ba9d1a8"}], "uid": "8fb3a91ba5aaf9de24cc8a92edc82b5d"}]}