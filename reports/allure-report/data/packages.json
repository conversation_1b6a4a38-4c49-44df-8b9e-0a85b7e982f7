{"uid": "83edc06c07f9ae9e47eb6dd1b683e4e2", "name": "packages", "children": [{"name": "testcases.test_ella.test_bluetooth_command", "children": [{"name": "测试open bluetooth命令", "uid": "80d93ad84c102297", "parentUid": "4acef881a24d816173fa62427d124608", "status": "failed", "time": {"start": 1750679165655, "stop": 1750679186246, "duration": 20591}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试close bluetooth命令", "uid": "835bb1536d04b219", "parentUid": "4acef881a24d816173fa62427d124608", "status": "passed", "time": {"start": 1750679189720, "stop": 1750679203912, "duration": 14192}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试蓝牙状态查询命令", "uid": "9223599218a3b939", "parentUid": "4acef881a24d816173fa62427d124608", "status": "passed", "time": {"start": 1750679206967, "stop": 1750679305491, "duration": 98524}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["regression"]}], "uid": "testcases.test_ella.test_bluetooth_command"}]}