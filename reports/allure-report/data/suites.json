{"uid": "98d3104e051c652961429bf95fa0b5d6", "name": "suites", "children": [{"name": "testcases.test_ella", "children": [{"name": "test_bluetooth_command", "children": [{"name": "TestEllaBluetoothCommand", "children": [{"name": "测试open bluetooth命令", "uid": "80d93ad84c102297", "parentUid": "b1a6082d32710292e7a1b2722efcc7bd", "status": "failed", "time": {"start": 1750679165655, "stop": 1750679186246, "duration": 20591}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试close bluetooth命令", "uid": "835bb1536d04b219", "parentUid": "b1a6082d32710292e7a1b2722efcc7bd", "status": "passed", "time": {"start": 1750679189720, "stop": 1750679203912, "duration": 14192}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试蓝牙状态查询命令", "uid": "9223599218a3b939", "parentUid": "b1a6082d32710292e7a1b2722efcc7bd", "status": "passed", "time": {"start": 1750679206967, "stop": 1750679305491, "duration": 98524}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["regression"]}], "uid": "b1a6082d32710292e7a1b2722efcc7bd"}], "uid": "b7338ce76f4b8b55035d86925d09ff19"}], "uid": "94b4d2a35e7a0e6ad70c14ed2152a386"}]}