"""
Ella语音助手蓝牙命令测试 - 简洁版本
使用基类和装饰器简化测试编写，提供更清晰的测试结构
"""
import pytest
import allure
from testcases.test_ella.base_ella_test import SimpleEllaTest
from core.logger import log


@allure.feature("Ella语音助手")
@allure.story("联系人控制命令 - 简洁版本")
class TestEllaCommandConcise(SimpleEllaTest):
    """Ella联系人命令测试类 - 简洁版本"""

    @allure.title("测试open contact命令 - 简洁版本")
    @allure.description("使用简化的测试框架测试联系人开启命令，验证响应包含Done且实际打开Dalier")
    @allure.severity(allure.severity_level.CRITICAL)
    @pytest.mark.smoke
    def test_open_bluetooth(self, ella_app):
        """测试open contact命令 - 简洁版本"""
        command = "open bluetooth"

        app_name = 'bluetooth'

        with allure.step(f"执行命令: {command}"):
            initial_status, final_status, response_text = self.simple_command_test(
                ella_app, command
            )

        with allure.step("验证响应包含Done"):
            response_lower = response_text.lower()
            expected_text =["bluetooth",'is turned on now']
            result = self.verify_expected_in_response(expected_text, response_lower)
            assert result, f"响应文本应包含'Done'，实际响应: '{response_text}'"

        with allure.step(f"验证{app_name}已打开"):
            assert final_status, f"{app_name}: 初始={initial_status}, 最终={final_status}, 响应='{response_text}'"

        with allure.step("记录测试结果"):
            summary = self.create_test_summary(command, initial_status, final_status, response_text)
            # 添加额外的验证信息
            self.attach_test_summary(summary)
            self.take_screenshot(ella_app, "test_completed")

        return initial_status, final_status, response_text
